@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --purple: #6c567b;
    --dark-blue: #394867;
    --red: #e85a65;
    --light-grey: #cccccc;
    --bg-light-blue: #e0e7ff;
    --bg-light-pink: #fdecef;
    --bg-wave-pink: #f6dce5;
    --text-primary: #0b0643;
}

* {
    font-family: Helvetica, sans-serif !important;
    color: #0b0643;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    padding: clamp(0.3rem, 1vw, 0.8rem);
}

body,
html {
    overflow: hidden;
}

/* Responsive media defaults */
img, video {
    max-width: 100%;
    height: auto;
}

/* Responsive padding adjustments */
@media (max-width: 768px) {
    body {
        padding: clamp(0.5rem, 3vw, 1rem);
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    body {
        padding: clamp(0.4rem, 1.2vw, 0.8rem);
    }
}

@media (min-width: 1000px) {
    body {
        padding: clamp(0.3rem, 0.8vw, 0.6rem);
        overflow: hidden;
    }

    .container {
        height: 100vh;
        max-height: 100vh;
        overflow: hidden;
    }
}

@media (min-width: 1025px) and (max-width: 1366px) {
    body {
        padding: clamp(0.5rem, 1vw, 0.8rem);
    }
}

@media (min-width: 1367px) {
    body {
        padding: clamp(0.5rem, 0.8vw, 1rem);
    }
}

/* Grid system tweaks to avoid horizontal overflow */
@media (max-width: 1024px) {
    .row {
        margin-left: 0;
        margin-right: 0;
    }
    [class*="col-"] {
        padding-left: 8px;
        padding-right: 8px;
    }
}


/* Prevent oversized elements from causing horizontal overflow */
.container, .row, [class*="col-"] {
    max-width: 100%;
    box-sizing: border-box;
}

/* Enhanced container responsiveness */
.container {
    width: 100%;
    margin: 0 auto;
    padding-left: clamp(0.5rem, 2vw, 1.5rem);
    padding-right: clamp(0.5rem, 2vw, 1.5rem);
}

/* Responsive grid adjustments for larger screens */
@media (min-width: 1025px) {
    .row {
        margin-left: clamp(-10px, -1vw, -15px);
        margin-right: clamp(-10px, -1vw, -15px);
    }

    [class*="col-"] {
        padding-left: clamp(10px, 1vw, 15px);
        padding-right: clamp(10px, 1vw, 15px);
    }
}

@media (min-width: 1367px) {
    .row {
        margin-left: clamp(-15px, -1.2vw, -20px);
        margin-right: clamp(-15px, -1.2vw, -20px);
    }

    [class*="col-"] {
        padding-left: clamp(15px, 1.2vw, 20px);
        padding-right: clamp(15px, 1.2vw, 20px);
    }
}


.text-verysmall {
    font-size: clamp(0.3rem, 1vw, 0.4rem) !important;
    font-weight: bold;
}

.card:hover {
    transform: translateY(-4px); /* Geser ke atas */
    box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2),
        inset 0 -4px 6px rgba(0, 0, 0, 0.3),
        inset 0 3px 4px rgba(255, 255, 255, 0.9);
}

.text-small {
    font-size: clamp(0.5rem, 1.5vw, 0.6rem) !important;
}

.text-sm {
    font-size: clamp(0.7rem, 2vw, 0.8rem) !important;
}
.text-sm2 {
    font-size: clamp(0.9rem, 2.5vw, 1rem) !important;
}
.text-large {
    font-size: clamp(1rem, 3vw, 1.2rem) !important;
}
.text-xlarge {
    font-size: clamp(1.2rem, 4vw, 1.5rem) !important;
    font-weight: bold;
}
.text-xxlarge {
    font-size: clamp(1.5rem, 5vw, 2rem) !important;
    font-weight: bold;
}

/* Basic Row */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -15px;
    margin-right: -15px;
}

/* Basic Column */
[class*="col-"] {
    padding-left: 15px;
    padding-right: 15px;
    box-sizing: border-box;
}

/* 12-column grid (responsive) */
.col-1 {
    flex: 0 0 8.33%;
    max-width: 8.33%;
}
.col-2 {
    flex: 0 0 16.66%;
    max-width: 16.66%;
}
.col-3 {
    flex: 0 0 25%;
    max-width: 25%;
}
.col-4 {
    flex: 0 0 33.33%;
    max-width: 33.33%;
}
.col-5 {
    flex: 0 0 41.66%;
    max-width: 41.66%;
}
.col-6 {
    flex: 0 0 50%;
    max-width: 50%;
}
.col-7 {
    flex: 0 0 58.33%;
    max-width: 58.33%;
}
.col-8 {
    flex: 0 0 66.66%;
    max-width: 66.66%;
}
.col-9 {
    flex: 0 0 75%;
    max-width: 75%;
}
.col-10 {
    flex: 0 0 83.33%;
    max-width: 83.33%;
}
.col-11 {
    flex: 0 0 91.66%;
    max-width: 91.66%;
}
.col-12 {
    flex: 0 0 100%;
    max-width: 100%;
}

/* Responsive: col-md-* for ≥768px */
@media (min-width: 768px) {
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    .col-md-4 {
        flex: 0 0 33.33%;
        max-width: 33.33%;
    }
    /* Tambah sesuai kebutuhan */
}

/* Responsive: col-lg-* for ≥992px */
@media (min-width: 992px) {
    .col-lg-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    .col-lg-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    /* Tambah sesuai kebutuhan */
}

/* bagian logo dan nama atas ------------------------------------------------*/
.div1 {
    grid-column: span 5 / span 5;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    text-align: right;
    width: 100%;
    height: fit-content;
}

.right-logo {
    display: flex;
    flex-direction: column;
    align-items: flex-end; /* Rata kanan isi dalam kolom */
    text-align: right;
}
.logo-line {
    display: flex;
    align-items: center;
    text-align: right;
}

.right-logo img {
    max-height: clamp(30px, 5vw, 50px);
}

.right-logo .slogan {
    font-weight: bold;
    font-size: clamp(1rem, 3vw, 1.5rem);
    margin-left: clamp(0.3rem, 1vw, 0.7rem);
    align-items: center;
    justify-content: center;
}

/* bagian logo dan nama atas  ------------------------------------------------*/

/* style neumorphism */
.neumorphism {
    background: #dfeff1;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15),
        inset 0 -3px 5px rgba(0, 0, 0, 0.2),
        inset 0 2px 3px rgba(255, 255, 255, 0.8);
    transition: all 0.2s ease-in-out;
}

.neumorphism:hover {
    transform: scale(1.02);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.2),
        inset 0 -4px 6px rgba(0, 0, 0, 0.3),
        inset 0 3px 4px rgba(255, 255, 255, 0.9);
    transition: all 0.2s ease-in-out;
}

.neumorphism:active {
    box-shadow: inset 2px 2px 1px #ffffff, inset -2px -2px 1px #ffffff;
}

/* style icon */

.imgicon {
    padding: clamp(2px, 0.5vw, 4px);
    width: clamp(30px, 5vw, 45px);
}

.imgicon-blue {
    filter: brightness(0) saturate(100%) invert(10%) sepia(91%) saturate(4842%)
        hue-rotate(238deg) brightness(85%) contrast(100%);
}

.imgicon-yellow {
    filter: brightness(0) saturate(100%) invert(47%) sepia(77%) saturate(4365%)
        hue-rotate(163deg) brightness(84%) contrast(93%);
}

.imgicon-gold {
    filter: brightness(0) saturate(100%) invert(33%) sepia(20%) saturate(1812%)
        hue-rotate(61deg) brightness(94%) contrast(95%);
}

.imgicon-purple {
    filter: brightness(0) saturate(100%) invert(14%) sepia(87%) saturate(4094%)
        hue-rotate(265deg) brightness(96%) contrast(104%);
}

.imgicon-red {
    filter: brightness(0) saturate(100%) invert(21%) sepia(93%) saturate(7494%)
        hue-rotate(358deg) brightness(94%) contrast(105%);
}

.imgicon-orange {
    filter: brightness(0) saturate(100%) invert(55%) sepia(89%) saturate(1245%)
        hue-rotate(1deg) brightness(100%) contrast(95%);
}

.imgicon-green {
    filter: brightness(0) saturate(100%) invert(47%) sepia(81%) saturate(502%)
        hue-rotate(81deg) brightness(92%) contrast(89%);
}

/* --- Home Button --- */
.home-button {
    position: absolute;
    bottom: clamp(0.3rem, 1vw, 0.8rem);
    right: clamp(0.3rem, 1vw, 0.8rem);
    background-color: #e9eafc;
    border-radius: clamp(6px, 1vw, 10px);
    padding: clamp(0.1rem, 0.5vw, 0.2rem) clamp(0.4rem, 1vw, 0.6rem);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: var(--purple);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.home-button:hover {
    transform: translateY(-3px);
}

.home-button svg {
    color: var(--purple);
}

.home-button span {
    font-size: clamp(0.5rem, 1.5vw, 0.6rem);
    font-weight: 600;
    margin-top: 1px;
}

@media (max-width: 992px) {
    .home-button {
        bottom: clamp(0.5rem, 3vw, 1rem);
        right: clamp(0.5rem, 3vw, 1rem);
    }
}

/* Padding */
/* All sides */
.p-1 {
    padding: 0.25rem;
}
.p-2 {
    padding: 0.5rem;
}
.p-3 {
    padding: 0.75rem;
}
.p-4 {
    padding: 1rem;
}
.p-5 {
    padding: 1.25rem;
}
.p-6 {
    padding: 1.5rem;
}
.p-7 {
    padding: 1.75rem;
}
.p-8 {
    padding: 2rem;
}
.p-9 {
    padding: 2.25rem;
}
.p-10 {
    padding: 2.5rem;
}

/* Horizontal (left + right) */
.px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
}
.px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}
.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}
.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}
.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}
.px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}
.px-7 {
    padding-left: 1.75rem;
    padding-right: 1.75rem;
}
.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}
.px-9 {
    padding-left: 2.25rem;
    padding-right: 2.25rem;
}
.px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
}

/* Vertical (top + bottom) */
.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}
.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}
.py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}
.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}
.py-5 {
    padding-top: 1.25rem;
    padding-bottom: 1.25rem;
}
.py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
}
.py-7 {
    padding-top: 1.75rem;
    padding-bottom: 1.75rem;
}
.py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
}
.py-9 {
    padding-top: 2.25rem;
    padding-bottom: 2.25rem;
}
.py-10 {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
}

.card {
    background-color: var(--bg-card);
    border-radius: 10px;
    background-color: rgb(241, 249, 250);
    box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.4);
    transition: all 0.2s ease-in-out;
}
.smooth-shadow {
    box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.4);
}
