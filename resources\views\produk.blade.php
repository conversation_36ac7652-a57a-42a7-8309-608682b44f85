<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Katalog Part</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
        :root {
            --primary-blue: #3a7bd5;
            --primary-purple: #8e44ad;
            --light-bg: #f0f2f5;
            --card-bg: #ffffff;
            --text-dark: #333;
            --text-light: #555;
            --border-color: #e0e0e0;
            --green-discount: #2ecc71;
            --red-warranty: #e74c3c;
            --blue-button: #4a90e2;
        }

        * {
            font-family: Helvetica, sans-serif !important;
            font-size: clamp(0.7rem, 1.5vw, 0.8rem) !important;
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body, html {
            overflow: hidden;
            height: 100vh;
            max-height: 100vh;
        }

        html,
        body {
            font-family: Helvetica, sans-serif !important;
            height: 100%;
            width: 100%;
            overflow: hidden;
            font-size: 16px;
        }

        body {
            display: flex;
            flex-direction: column;
            background-image:
                linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
                url('{{ asset('images/bg.png') }}');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            padding: clamp(8px, 1.5vw, 15px);
        }

        /* Header */
        .header {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-bottom: clamp(8px, 1.5vw, 15px);
            text-align: right;
        }

        .header .logo-text {
            display: flex;
            align-items: center;
        }

        .header .logo {
            width: 50px;
            height: 50px;
            background-color: var(--primary-purple);
            border-radius: 8px;
            margin-left: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 24px;
        }

        .header .company-info h1 {
            margin: 0;
            font-size: 1.5rem;
            color: #3f3d56;
        }

        .header .company-info p {
            margin: 0;
            font-size: 0.9rem;
            color: var(--text-light);
        }

        /* Search Bar */
        .search-container {
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 4px 10px;
            border: 1px solid var(--border-color);
            border-radius: 25px;
            font-size: 1rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .search-input::placeholder {
            color: #aaa;
        }

        /* Main Content */
        .main-content {
            display: flex;
            flex-direction: column;
            gap: clamp(8px, 1.5vw, 15px);
            height: calc(100vh - clamp(80px, 12vw, 120px));
            overflow: hidden;
        }

        /* Product Grid */
        .product-section {
            flex: 1;
            position: relative;
        }

        .product-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: clamp(3px, 0.5vw, 5px);
            overflow-y: auto;
            max-height: 100%;

            /* Responsive columns to avoid horizontal scroll */
            @media (max-width: 767.98px) {
                .product-grid {
                    grid-template-columns: 1fr;
                }
            }
            @media (min-width: 768px) and (max-width: 991.98px) {
                .product-grid {
                    grid-template-columns: repeat(2, 1fr);
                }
            }
            @media (min-width: 992px) {
                .product-grid {
                    grid-template-columns: repeat(3, 1fr);
                }
            }

        }

        .product-card {
            background-color: var(--card-bg);
            border-radius: 12px;
            padding: 12px;
            text-align: center;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .product-card .tag {
            position: absolute;
            top: 0;
            left: 0;
            padding: 2px 4px;
            font-size: 0.8rem !important;
            font-weight: 600;
            color: white;
            border-bottom-right-radius: 10px;
        }

        .tag.warranty {
            background-color: var(--blue-button);
        }

        .tag.discount-50 {
            background-color: var(--green-discount);
        }

        .tag.discount-10 {
            background-color: var(--green-discount);
            opacity: 0.8;
        }

        .product-card img {
            width: 100%;
            height: fit-content;
        }

        .product-card h3 {
            margin: 8px 0;
            font-size: 0.9rem;
            color: var(--text-dark);
            flex-grow: 1;
        }

        .price-list-btn {
            background: linear-gradient(45deg, #4776E6, #8E54E9);
            color: white;
            border: none;
            padding: 2px 4px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: background 0.3s ease;
            width: 100%;
        }

        /* Details Section */
        .details-section {
            flex: 1.5;
            background: linear-gradient(135deg, #f5f7fa, #d3dbe76c);
            padding: 15px;
            border-radius: 15px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .details-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .details-header img {
            width: 180px;
            height: fit-content;
            object-fit: contain;
            border-radius: 12px;
            padding: 8px;
        }

        .details-header h2 {
            font-size: 1.3rem;
            color: #3f3d56;
        }

        /* DataTables Styling */
        #priceTable {
            width: 100% !important;
            border-collapse: collapse;
            font-size: 0.85rem;
        }

        #priceTable thead th {
            text-align: left;
            padding: 10px 12px;
            background-color: transparent;
            border-bottom: 2px solid rgba(0, 0, 0, 0.1);
            font-weight: 600;
            color: var(--text-light);
        }

        #priceTable tbody td {
            padding: 6px 8px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            vertical-align: middle;
        }

        #priceTable tbody tr:last-child td {
            border-bottom: none;
        }

        .note-warranty {
            color: var(--red-warranty);
            font-weight: 500;
        }

        .note-discount {
            color: var(--green-discount);
            font-weight: 500;
        }

        /* Table Container */
        .table-container {
            flex: 1;
            overflow: auto;
        }

        /* Pagination */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 15px;
            gap: 8px;
            color: white;
        }

        .pagination a {
            color: white;
        }

        .pagination a {
            color: var(--text-light);
            text-decoration: none;
            padding: 6px 12px;
            border-radius: 6px;
            transition: background-color 0.3s ease, color 0.3s ease;
            font-size: 0.9rem;
        }

        .pagination a.active {
            background-color: var(--blue-button);
            color: white;
            font-weight: 600;
        }

        /* Home Button */
        .home-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background-color: white;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            cursor: pointer;
            text-decoration: none;
            color: var(--text-dark);
            font-size: 0.7rem;
            font-weight: 600;
            transition: transform 0.2s;
        }

        .home-icon {
            width: 28px;
            height: 28px;
            margin-bottom: 2px;
        }

        /* Responsive adjustments */
        @media (min-width: 768px) {
            .main-content {
                flex-direction: row;
                height: calc(100% - 140px);
            }

            .product-section {
                height: 100%;
            }

            .product-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .details-section {
                height: 100%;
            }
        }

        @media (min-width: 992px) {
            .product-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        .smooth-shadow {
            box-shadow: 0 1px 3px -1px rgba(0, 0, 0, 0.4);
        }

        .highlight {
            background-color: #a0c9ffc0 !important;
            color: white;
        }

        @media (min-width: 1000px) {
            body {
                padding: clamp(5px, 1vw, 10px);
            }

            .header {
                margin-bottom: clamp(5px, 1vw, 10px);
            }

            .main-content {
                gap: clamp(5px, 1vw, 10px);
                height: calc(100vh - clamp(60px, 8vw, 80px));
            }

            .home-button {
                bottom: clamp(10px, 1.5vw, 15px);
                right: clamp(10px, 1.5vw, 15px);
                width: clamp(45px, 6vw, 55px);
                height: clamp(45px, 6vw, 55px);
                font-size: clamp(0.6rem, 1.2vw, 0.7rem);
            }

            .home-icon {
                width: clamp(20px, 3vw, 25px);
                height: clamp(20px, 3vw, 25px);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <header class="header">
            <div class="div1 header">
                <div class="right-logo">
                    <div class="logo-line">
                        <img src="{{ asset('images/logo.png') }}" alt="Logo" />
                        <p class="slogan" style="font-size: 1.5rem !important;">PT. PUTERA WIBOWO BORNEO</p>
                    </div>
                    <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"
                    </div>
                </div>
            </div>
        </header>
        <main class="main-content">
            <section class="product-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Cari Nama Part">
                </div>
                <div class="product-grid">
                    <!-- Row 1 -->
                    <div class="product-card highlight">
                        <div class="tag warranty">Warranty 3 Bulan</div>
                        <img src="{{ asset('images/product/compressor.png') }}" alt="Compressor">
                        <h5 class="m-0 p-0">COMPRESSOR</h3>
                            <button class="price-list-btn">Price List</button>
                    </div>
                    <div class="product-card">
                        <div class="tag discount-50">Diskon 50 %</div>
                        <img src="{{ asset('images/product/compressor.png') }}" alt="Evaporator">
                        <h5 class="m-0 p-0">EVAPORATOR</h3>
                            <button class="price-list-btn">Price List</button>
                    </div>
                    <div class="product-card">
                        <div class="tag discount-10">Diskon 10 %</div>
                        <img src="{{ asset('images/product/compressor.png') }}" alt="Motor Blower">
                        <h5 class="m-0 p-0">MOTOR BLOWER</h3>
                            <button class="price-list-btn">Price List</button>
                    </div>
                    <!-- Row 2 -->
                    <div class="product-card">
                        <img src="{{ asset('images/product/compressor.png') }}" alt="Compressor">
                        <h5 class="m-0 p-0">COMPRESSOR</h3>
                            <button class="price-list-btn">Price List</button>
                    </div>
                    <div class="product-card">
                        <img src="{{ asset('images/product/compressor.png') }}" alt="Evaporator">
                        <h5 class="m-0 p-0">EVAPORATOR</h3>
                            <button class="price-list-btn">Price List</button>
                    </div>
                    <div class="product-card">
                        <img src="{{ asset('images/product/compressor.png') }}" alt="Motor Blower">
                        <h5 class="m-0 p-0">MOTOR BLOWER</h3>
                            <button class="price-list-btn">Price List</button>
                    </div>
                    <div class="product-card">
                        <img src="{{ asset('images/product/compressor.png') }}" alt="Evaporator">
                        <h5 class="m-0 p-0">EVAPORATOR</h3>
                            <button class="price-list-btn">Price List</button>
                    </div>
                    <div class="product-card">
                        <img src="{{ asset('images/product/compressor.png') }}" alt="Motor Blower">
                        <h5 class="m-0 p-0">MOTOR BLOWER</h3>
                            <button class="price-list-btn">Price List</button>
                    </div>
                </div>
                <div class="pagination">
                    <a href="#">1</a>
                    <a href="#" class="active">2</a>
                    <a href="#">3</a>
                    <a href="#">4</a>
                    <a href="#">5</a>
                </div>
            </section>

            <section class="details-section smooth-shadow">
                <div class="details-header">
                    <img src="{{ asset('images/product/compressor.png') }}" alt="Selected Compressor">
                    <h2>COMPRESSOR</h2>
                </div>
                <div class="table-container">
                    <table id="priceTable" class="display">
                        <thead>
                            <tr>
                                <th>Merek</th>
                                <th>Unit Model</th>
                                <th>Price</th>
                                <th>Keterangan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>SANNY</td>
                                <td>SY500</td>
                                <td>Rp. 6.000.000</td>
                                <td>Warranty 3 Bulan</td>
                            </tr>
                            <tr>
                                <td>SANNY</td>
                                <td>SY215 STD</td>
                                <td>Rp. 5.780.000</td>
                                <td>Warranty 6 Bulan</td>
                            </tr>
                            <tr>
                                <td>CAT</td>
                                <td>16HD</td>
                                <td>Rp. 5.600.000</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>SANNY</td>
                                <td>SY215 STD</td>
                                <td>Rp. 5.780.000</td>
                                <td>Diskon 50 %</td>
                            </tr>
                            <tr>
                                <td>SANNY</td>
                                <td>SY500</td>
                                <td>Rp. 6.000.000</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>SANNY</td>
                                <td>SY215 STD</td>
                                <td>Rp. 5.780.000</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>SANNY</td>
                                <td>SY500</td>
                                <td>Rp. 6.000.000</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>SANNY</td>
                                <td>SY215 STD</td>
                                <td>Rp. 5.780.000</td>
                                <td>Diskon 50 %</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        </main>
    </div>
    </a>
    <!-- FLOATING HOME BUTTON -->
    <a href="{{ route('home') }}" class="card home-button neumorphism">
        <img class="imgicon-blue" src="{{ asset('assets/icon/home.png') }}" alt="Home" width="30" height="30">
        </img>
        <span style="text-primary">HOME</span>
    </a>
    <script>
        $(document).ready(function () {
            $('#priceTable').DataTable({
                "paging": false,
                "searching": false,
                "info": false,
                "ordering": false,
                "scrollY": "calc(100% - 150px)",
                "scrollCollapse": true,
                "createdRow": function (row, data, dataIndex) {
                    let noteText = data[3];
                    if (noteText.includes('Warranty')) {
                        $(row).find('td:eq(3)').addClass('note-warranty');
                    } else if (noteText.includes('Diskon')) {
                        $(row).find('td:eq(3)').addClass('note-discount');
                    }
                }
            });
        });
    </script>
</body>

</html>