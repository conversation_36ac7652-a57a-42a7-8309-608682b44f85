* {
    margin: 10;
    padding: 0;
    box-sizing: border-box;
}

body,
html {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    max-width: 100vw;
    max-height: 100vh;
}

/* Main content layout improvements */
.content-section-top {
    flex: 0 0 auto;
    height: clamp(180px, 35vh, 250px);
    margin-bottom: clamp(0.5rem, 2vh, 1rem);
}

.content-section-bottom {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

/* Service history improvements */
.service-history-box {
    display: flex;
    flex-direction: column;
    padding: clamp(0.5rem, 2vw, 1rem) !important;
}

.service-history-title {
    font-size: clamp(0.7rem, 2vw, 0.9rem) !important;
    margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.service-history-list {
    margin: 0;
    padding-left: clamp(0.8rem, 2vw, 1.2rem);
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
}

.service-item {
    font-size: clamp(0.6rem, 1.8vw, 0.8rem) !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: clamp(0.2rem, 0.5vw, 0.3rem);
}

/* Mitra card text improvements */
.mitra-text {
    flex: 1;
    min-width: 0;
}

.mitra-company-name {
    font-size: clamp(0.6rem, 1.8vw, 0.8rem) !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: clamp(0.1rem, 0.3vw, 0.2rem);
}

.mitra-period {
    font-size: clamp(0.5rem, 1.5vw, 0.7rem) !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Stats box improvements */
.stats-box {
    padding: clamp(0.3rem, 1.5vw, 0.6rem) !important;
    margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
    display: flex;
    flex-direction: column;
    height: auto;
}

.stats-number {
    font-size: clamp(1.5rem, 4vw, 2.2rem) !important;
    line-height: 1;
}

.stats-icon {
    max-width: clamp(35px, 6vw, 50px);
    height: auto;
}

.stats-label {
    font-size: clamp(0.6rem, 1.8vw, 0.8rem) !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-top: clamp(0.2rem, 0.5vw, 0.3rem);
}
.flip {
    animation: flip 0.5s ease-in-out;
}

@keyframes flip {
    0% {
        transform: rotateX(0deg);
    }
    50% {
        transform: rotateX(-90deg);
    }
    100% {
        transform: rotateX(0deg);
    }
}

.parent {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-auto-rows: min-content;
    gap: clamp(4px, 0.5vw, 6px);
    max-height: 100%;
    overflow: hidden;
}

.div2 {
    grid-column: span 5 / span 5;
    grid-row: span 2 / span 2;
    grid-row-start: 2;
}

.div3 {
    grid-column: span 2 / span 2;
    grid-row-start: 4;
}

.div4 {
    grid-column: span 3 / span 3;
    grid-column-start: 3;
    grid-row-start: 4;
}

.div5 {
    grid-column: span 5 / span 5;
    grid-row-start: 5;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 100vh;
    justify-content: space-between;
    padding: clamp(0.3rem, 1vw, 0.8rem);
    max-width: 100vw;
    overflow: hidden;
    box-sizing: border-box;
}

.top-section {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.left-info {
    max-width: 50%;
}

.left-info .weather {
    display: flex;
    align-items: center;
    font-size: clamp(0.8rem, 2vw, 1rem);
    margin-bottom: clamp(0.2rem, 0.5vw, 0.3rem);
}
#iconcuaca {
    width: auto;
    height: auto;
    display: block;
}

.left-info .weather img {
    height: clamp(30px, 4vw, 40px);
    margin-right: clamp(0.2rem, 0.5vw, 0.3rem);
}
.ketderajat span {
    font-size: clamp(0.8rem, 2vw, 1rem);
}

.ketderajat {
    display: flex;
    flex-direction: column;
    justify-content: end;
    align-items: flex-start;
    font-size: 0.9rem;
    line-height: 1.2;
}

.time {
    padding: 0;
    margin: 0;
    font-size: clamp(3rem, 8vw, 5rem);
    color: #5b2991;
    line-height: 1;
    display: flex;
    align-items: stretch;
}

.date {
    line-height: 1;
    font-size: clamp(1rem, 3vw, 1.5rem);
    margin-top: 0.3rem;
    color: #5b2991;
}

/* Enhanced responsive typography scaling */


@media (min-width: 1025px) and (max-width: 1366px) {
    .time { font-size: clamp(2.5rem, 6rem, 10rem); }
    .date { font-size: clamp(0.9rem, 1.5vw, 1.2rem); }
}


.holiday {
    font-size: 1rem;
    font-style: italic;
    margin-bottom: 1rem;
}

/* TOP card - hanya bagian bawah terlihat */
.card-top {
    top: 0;
    opacity: 0.5;
    z-index: 1;
    transform: translateY(-25%); /* geser agar hanya bagian bawah muncul */
}

/* MIDDLE card - utama */
.card-middle {
    top: 50%;
    transform: translateY(-50%); /* posisikan benar-benar di tengah */
    opacity: 1;
    z-index: 3;
}

/* BOTTOM card - hanya bagian atas terlihat */
.card-bottom {
    bottom: 0;
    opacity: 0.5;
    z-index: 1;
    transform: translateY(25%); /* geser agar hanya bagian atas muncul */
}

@keyframes slide {
    0%,
    20% {
        transform: translateX(0);
    }
    33%,
    53% {
        transform: translateX(-100vw);
    }
    66%,
    86% {
        transform: translateX(-200vw);
    }
    100% {
        transform: translateX(0);
    }
}

.info-section {
    gap: 10px;
    display: flex;
    /* justify-content: space-between; */
    /* flex-wrap: wrap; */
}

.info-box {
    background: white;
    border-radius: clamp(8px, 2vw, 12px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.209);
    flex: 1;
    padding: clamp(0.5rem, 2vw, 1rem);
}

.info-box p {
    font-size: clamp(0.8rem, 2.5vw, 1.1rem);
}

.info-box ul {
    margin-top: clamp(0.3rem, 1vw, 0.7rem);
    font-size: clamp(0.7rem, 2vw, 1rem);
    padding-left: clamp(0.8rem, 2vw, 1.2rem);
}

.bottom-nav {
    display: flex;
    justify-content: space-around;
    border-radius: clamp(6px, 1vw, 10px);
    padding: clamp(0.3rem, 1vw, 0.6rem);
    gap: clamp(0.3rem, 1vw, 0.6rem);
}

.bottom-nav button {
    background: white;
    border: none;
    border-radius: clamp(6px, 1vw, 8px);
    padding: clamp(0.2rem, 0.8vw, 0.4rem);
    font-size: clamp(0.6rem, 1.5vw, 0.7rem);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    flex: 1;
    margin: 0 clamp(0.1rem, 0.5vw, 0.2rem);
}

/* Responsive layout adjustments */
@media (max-width: 768px) {
    .left-info,
    .right-logo {
        max-width: 100%;
    }

    .mitra-cards {
        flex-direction: column;
    }

    .content-section-top {
        height: clamp(150px, 30vh, 200px);
        margin-bottom: clamp(0.3rem, 1vh, 0.5rem);
    }

    .holiday {
        font-size: clamp(0.7rem, 2.5vw, 0.9rem);
        margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
    }

    .service-history-title {
        font-size: clamp(0.6rem, 2.5vw, 0.8rem) !important;
    }

    .service-item {
        font-size: clamp(0.5rem, 2vw, 0.7rem) !important;
    }

    .mitra-company-name {
        font-size: clamp(0.5rem, 2vw, 0.7rem) !important;
    }

    .mitra-period {
        font-size: clamp(0.4rem, 1.8vw, 0.6rem) !important;
    }

    .stats-number {
        font-size: clamp(1.2rem, 5vw, 1.8rem) !important;
    }

    .stats-label {
        font-size: clamp(0.5rem, 2vw, 0.7rem) !important;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {

    .mitra-cards-stack {
        height: clamp(100px, 12vw, 120px);
    }

    .content-section-top {
        height: clamp(160px, 32vh, 220px);
        margin-bottom: clamp(0.4rem, 1.5vh, 0.7rem);
    }

    .service-history-title {
        font-size: clamp(0.7rem, 2.2vw, 0.9rem) !important;
    }

    .service-item {
        font-size: clamp(0.6rem, 1.9vw, 0.8rem) !important;
    }

    .mitra-company-name {
        font-size: clamp(0.6rem, 1.9vw, 0.8rem) !important;
    }

    .mitra-period {
        font-size: clamp(0.5rem, 1.6vw, 0.7rem) !important;
    }

    .stats-number {
        font-size: clamp(1.8rem, 4.5vw, 2.3rem) !important;
    }

    .stats-label {
        font-size: clamp(0.6rem, 1.9vw, 0.8rem) !important;
    }
}

@media (min-width: 1025px) and (max-width: 1366px) {

    .mitra-cards-stack {
        height: clamp(600px,100vw, 800px);
    }

    .slider-wrapper {
        height: clamp(210px, 12vw, 230px);
    }

    .content-section-top {
        height: clamp(170px, 33vh, 230px);
        margin-bottom: clamp(0.5rem, 1.5vh, 0.8rem);
    }

    .service-history-title {
        font-size: clamp(0.7rem, 1.8vw, 0.9rem) !important;
    }

    .service-item {
        font-size: clamp(0.6rem, 1.5vw, 0.8rem) !important;
    }

    .mitra-company-name {
        font-size: clamp(0.6rem, 1.5vw, 0.8rem) !important;
    }

    .mitra-period {
        font-size: clamp(0.5rem, 1.3vw, 0.7rem) !important;
    }

    .stats-number {
        font-size: clamp(1.8rem, 3.5vw, 2.3rem) !important;
    }

    .stats-label {
        font-size: clamp(0.6rem, 1.5vw, 0.8rem) !important;
    }
}

@media (min-width: 1000px) {
    .container {
        height: 100vh;
        max-height: 100vh;
        overflow: hidden;
        padding: clamp(0.2rem, 0.5vw, 0.4rem);
    }

    .parent {
        gap: clamp(3px, 0.3vw, 4px);
        max-height: calc(100vh - 1rem);
        overflow: hidden;
    }


    .mitra-cards-stack {
        height: clamp(500px, 40vw, 7023px);
    }

    .slider-wrapper {
        height: clamp(150px, 8vw, 180px);
    }



    .date {
        font-size: clamp(0.8rem, 1vw, 1rem);
    }

    .content-section-top {
        height: clamp(140px, 28vh, 180px);
        margin-bottom: clamp(0.3rem, 1vh, 0.5rem);
    }

    .service-history-title {
        font-size: clamp(0.6rem, 1.2vw, 0.8rem) !important;
    }

    .service-item {
        font-size: clamp(0.5rem, 1vw, 0.7rem) !important;
    }

    .mitra-company-name {
        font-size: clamp(0.5rem, 1vw, 0.7rem) !important;
    }

    .mitra-period {
        font-size: clamp(0.4rem, 0.9vw, 0.6rem) !important;
    }

    .stats-number {
        font-size: clamp(1.5rem, 2.5vw, 2rem) !important;
    }

    .stats-label {
        font-size: clamp(0.5rem, 1vw, 0.7rem) !important;
    }
}

@media (min-width: 1367px) {

    .mitra-cards-stack {
        height: clamp(603px, 4232vw, 1280px);
    }

    .slider-wrapper {
        height: clamp(170px, 8vw, 210px);
    }

    .content-section-top {
        height: clamp(160px, 30vh, 200px);
        margin-bottom: clamp(0.4rem, 1.2vh, 0.6rem);
    }

    .service-history-title {
        font-size: clamp(0.7rem, 1.1vw, 0.9rem) !important;
    }

    .service-item {
        font-size: clamp(0.6rem, 0.9vw, 0.8rem) !important;
    }

    .mitra-company-name {
        font-size: clamp(0.6rem, 0.9vw, 0.8rem) !important;
    }

    .mitra-period {
        font-size: clamp(0.5rem, 0.8vw, 0.7rem) !important;
    }

    .stats-number {
        font-size: clamp(1.8rem, 2.2vw, 2.3rem) !important;
    }

    .stats-label {
        font-size: clamp(0.6rem, 0.9vw, 0.8rem) !important;
    }
}

.btnmenu {
    background: white;
    border-radius: 25px;
    padding: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1;
    margin: 0.5rem;
}

.mitra-cards-stack {
    position: relative;
    height: clamp(90px, 10vw, 120px); /* responsive height for card stacking */
    width: 100%;
}

.mitra-card {
    position: absolute;
    left: 0;
    width: 100%;
    background: white;
    border-radius: clamp(8px, 2vw, 15px);
    padding: clamp(0.3rem, 1.5vw, 0.7rem);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: clamp(0.5rem, 2vw, 1.2rem);
    transition: all 0.3s ease;
    font-size: clamp(0.7rem, 2.5vw, 1rem);
}

.mitra-card img {
    width: clamp(30px, 5vw, 50px);
    height: clamp(30px, 5vw, 50px);
    object-fit: contain;
}

/* Card atas (bagian bawahnya saja yang tampak) */
.card-top {
    top: 0;
    transform: translateY(-30%);
    opacity: 0.5;
    z-index: 1;
}

/* Card tengah (utama) */
.card-middle {
    margin-left: 20px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 1;
    z-index: 3;
}

/* Card bawah (bagian atasnya saja yang tampak) */
.card-bottom {
    bottom: 0;
    transform: translateY(30%);
    opacity: 0.5;
    z-index: 1;
}

/* animasi card */
.slider-wrapper {
    position: relative;
    width: 100%;
}

.slider-track {
    display: flex;
    width: 300%;
    transition: transform 0.5s ease-in-out;
}

.slide {
    width: 100%;
    flex-shrink: 0;
    border-radius: 10px;
}

.slider-dots {
    bottom: 10px;
}

.dot {
    height: 10px;
    width: 10px;
    margin: 0 5px;
    background-color: #bbb;
    border-radius: 50%;
    display: inline-block;
    border: none;
}

.dot.active,
.dot:hover {
    background-color: #717171;
}

/* image slide */
.slider-wrapper {
    height: clamp(230px, 25vw, 330px);
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.slider-track {
    display: flex;
    height: 100%;
    width: 100%;
}

.slide {
    width: 100%;
    height: 100%;
    flex-shrink: 0;
    border-radius: clamp(8px, 2vw, 12px);
    overflow: hidden;
    position: relative;
    border: 1px solid #ccc;
}

.slide-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    border-radius: clamp(8px, 2vw, 12px);
}

/* Additional layout improvements */
.row {
    margin-left: 0;
    margin-right: 0;
}

[class*="col-"] {
    padding-left: clamp(4px, 1vw, 8px);
    padding-right: clamp(4px, 1vw, 8px);
}

/* Ensure no overflow on any elements */
* {
    max-width: 100%;
    box-sizing: border-box;
}

/* Improved tagline styling */
.right-logo .tagline {
    font-size: clamp(0.6rem, 1.8vw, 0.9rem) !important;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

/* Additional responsive improvements for very small screens */
@media (max-width: 480px) {
    .tagline {
        font-size: clamp(0.4rem, 3vw, 0.6rem) !important;
    }

    .content-section-top {
        height: clamp(120px, 25vh, 160px);
    }

    .service-history-title {
        font-size: clamp(0.5rem, 3vw, 0.7rem) !important;
    }

    .service-item {
        font-size: clamp(0.4rem, 2.5vw, 0.6rem) !important;
    }

    .mitra-company-name {
        font-size: clamp(0.4rem, 2.5vw, 0.6rem) !important;
    }

    .mitra-period {
        font-size: clamp(0.3rem, 2vw, 0.5rem) !important;
    }

    .stats-number {
        font-size: clamp(1rem, 6vw, 1.5rem) !important;
    }

    .stats-label {
        font-size: clamp(0.4rem, 2.5vw, 0.6rem) !important;
    }
}

/* Holiday text improvements */
.holiday {
    font-size: clamp(0.7rem, 2vw, 1rem);
    font-style: italic;
    margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Ensure info boxes don't overflow */
.info-box {
    overflow: hidden;
    word-wrap: break-word;
}

.stats-box:last-child {
    margin-bottom: 0;
}
