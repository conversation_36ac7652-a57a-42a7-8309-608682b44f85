

/* --- Header --- */
.company-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 1rem;
    padding: 0 1.5rem 1.5rem 0;
    color: var(--purple);
}

.company-header .logo {
    padding: 0.5rem;
    background-color: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    justify-content: center;
}

.company-info h1 {
    font-size: 1.1rem;
    font-weight: 700;
    text-align: right;
}

.company-info p {
    font-size: 0.75rem;
    font-weight: 500;
    opacity: 0.9;
    text-align: right;
}

/* --- Main Layout --- */
.calendar-layout {
    display: flex;
    gap: clamp(1rem, 2vw, 2rem);
    height: calc(100vh - 2rem);
    max-height: calc(100vh - 2rem);
    overflow: hidden;
}

.side-panel{
    max-width: clamp(250px, 25vw, 300px);
    overflow-y: auto;
    max-height: 100%;
}

/* --- Glassmorphism Card Style --- */
.side-panel, .main-calendar-panel {
    width: 100%;
    background: rgba(255, 255, 255, 0.575);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-radius: clamp(15px, 2vw, 20px);
    padding: clamp(1rem, 2vw, 1.75rem);
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.18);
    overflow: hidden;
}

/* --- Left Side Panel --- */
.month-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: var(--purple);
}

.month-navigation h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.month-navigation .arrow {
    font-size: 1.5rem;
    font-weight: 300;
    cursor: pointer;
}

.mini-calendar {
    margin-bottom: 0.2rem;
}
.mini-calendar:last-child {
    margin-bottom: 0;
}

.mini-calendar h3 {
    color: var(--purple);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.mini-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    row-gap: 0.75rem;
    font-size: 0.9rem;
    color: var(--dark-blue);
}

.mini-grid span:nth-child(-n+7) { /* First 7 are day headers */
    font-weight: 600;
    color: var(--purple);
    font-size: 0.8rem;
}

.mini-grid .sunday {
    color: var(--red);
}

.mini-grid .inactive {
    color: var(--light-grey);
}

/* --- Main Calendar Panel --- */
.main-calendar-grid {

    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem 1rem;
    text-align: center;
}

.day-name {
    color: var(--purple);
    font-weight: 600;
    margin-bottom: 1rem;
}

.date-cell {
    font-size: 1.3rem;
    color: var(--dark-blue);
    padding: 0.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
}

.date-cell.inactive {
    color: var(--light-grey);
    font-weight: 400;
}

.date-cell.sunday, .date-cell.holiday {
    color: var(--red);
}

.date-cell.holiday {
    font-weight: 700;
}

.date-cell.highlighted {
    background-color: #fff;
    color: var(--dark-blue);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    font-weight: 600;
    justify-self: center;
}

.holiday-note {
    margin-top: 1.5rem;
    font-size: 0.9rem;
    color: var(--purple);
    font-weight: 500;
}

/* Prevent calendar grid overflow on tablets */
@media (max-width: 1024px) {
    .main-calendar-grid {
        gap: 0.5rem;
    }
}


/* --- Responsive Adjustments --- */
@media (max-width: 1000px) {
    .calendar-layout {
        flex-direction: column;
        gap: 1rem;
    }
    .side-panel {
        max-width: 100%;
    }
    .main-calendar-panel {
        margin-top: 1.5rem;
    }
    .company-header {
        justify-content: center;
        padding-right: 0;
    }
}

@media (max-width: 480px) {
    body {
        padding: 1rem;
    }
    .side-panel, .main-calendar-panel {
        padding: 1rem;
    }
    .date-cell, .date-cell.highlighted {
        font-size: 1rem;
        height: 40px;
    }
    .date-cell.highlighted {
        width: 40px;
    }
    .company-info h1 {
        font-size: 0.9rem;
    }
    .company-info p {
        font-size: 0.65rem;
    }
}

@media (min-width: 1000px) {
    body {
        padding: clamp(0.3rem, 0.8vw, 0.6rem);
        overflow: hidden;
        height: 100vh;
        max-height: 100vh;
    }

    .calendar-layout {
        gap: clamp(0.8rem, 1.5vw, 1.5rem);
        height: calc(100vh - 1rem);
        max-height: calc(100vh - 1rem);
    }

    .company-header {
        padding: 0 clamp(0.5rem, 1vw, 1rem) clamp(0.5rem, 1vw, 1rem) 0;
        gap: clamp(0.5rem, 1vw, 0.8rem);
    }

    .company-info h1 {
        font-size: clamp(0.8rem, 1.5vw, 1rem);
    }

    .company-info p {
        font-size: clamp(0.6rem, 1.2vw, 0.7rem);
    }

    .side-panel, .main-calendar-panel {
        padding: clamp(0.8rem, 1.5vw, 1.2rem);
    }
}